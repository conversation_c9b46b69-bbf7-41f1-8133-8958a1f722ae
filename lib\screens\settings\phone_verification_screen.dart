import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../utils/app_colors.dart';
import '../../utils/logger_utils.dart';

/// 전화번호 인증 화면
class PhoneVerificationScreen extends StatefulWidget {
  const PhoneVerificationScreen({super.key});

  @override
  State<PhoneVerificationScreen> createState() => _PhoneVerificationScreenState();
}

class _PhoneVerificationScreenState extends State<PhoneVerificationScreen> {
  // 웹뷰 관련
  WebViewController? _webViewController;
  
  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  /// 웹뷰 초기화
  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            LoggerUtils.logInfo('전화번호 인증 웹뷰 로드 완료: $url');
          },
          onWebResourceError: (WebResourceError error) {
            LoggerUtils.logError('웹뷰 로드 오류: ${error.description}');
          },
        ),
      )
      ..addJavaScriptChannel(
        'phoneVerificationSuccess',
        onMessageReceived: (JavaScriptMessage message) {
          LoggerUtils.logInfo('전화번호 인증 성공: ${message.message}');
          _handleVerificationSuccess(message.message);
        },
      )
      ..loadRequest(Uri.parse('https://parabara-1a504.web.app/phone-verification.html'));
  }

  /// 전화번호 인증 성공 처리
  void _handleVerificationSuccess(String message) {
    try {
      // JSON 파싱 (예: {"phoneNumber": "010-1234-5678", "verified": true})
      // 실제로는 더 복잡한 파싱이 필요할 수 있음
      LoggerUtils.logInfo('전화번호 인증 완료: $message');

      // 성공 처리 후 화면 닫기
      if (mounted) {
        Navigator.of(context).pop(true); // 성공 결과 반환
      }
    } catch (e) {
      LoggerUtils.logError('전화번호 인증 결과 처리 오류', error: e);
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('전화번호 인증'),
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: WebViewWidget(
          controller: _webViewController!,
        ),
      ),
    );
  }
}
